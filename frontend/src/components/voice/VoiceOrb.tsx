import React, { useEffect, useRef, useState } from 'react';
import { useVoiceStateVisuals, useEmotionAnimations } from '../../hooks/useEmotionVisuals';

export interface VoiceOrbProps {
  emotions?: Record<string, number>;
  isListening?: boolean;
  isProcessing?: boolean;
  isSpeaking?: boolean;
  isIdle?: boolean;
  intensity?: number;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

export default function VoiceOrb({
  emotions = {},
  isListening = false,
  isProcessing = false,
  isSpeaking = false,
  isIdle = true,
  intensity = 0.5,
  size = 'large',
  className = ''
}: VoiceOrbProps) {
  const orbRef = useRef<HTMLDivElement>(null);
  const [currentState, setCurrentState] = useState<'idle' | 'listening' | 'processing' | 'speaking'>('idle');

  // Use the enhanced emotion visuals hook
  const voiceVisuals = useVoiceStateVisuals(isListening, isProcessing, isSpeaking, emotions);
  const emotionAnimations = useEmotionAnimations(emotions, !isIdle);

  // Size configurations
  const sizeConfig = {
    small: { width: 120, height: 120, glowSize: 160 },
    medium: { width: 180, height: 180, glowSize: 240 },
    large: { width: 240, height: 240, glowSize: 320 }
  };

  const config = sizeConfig[size];

  // Update current state based on props
  useEffect(() => {
    if (isSpeaking) {
      setCurrentState('speaking');
    } else if (isProcessing) {
      setCurrentState('processing');
    } else if (isListening) {
      setCurrentState('listening');
    } else {
      setCurrentState('idle');
    }
  }, [isListening, isProcessing, isSpeaking, isIdle]);

  // Get state-specific animations and effects using the new emotion system
  const getStateEffects = () => {
    const baseIntensity = voiceVisuals.intensity * intensity;
    const animationSpeed = emotionAnimations.animationSpeed;

    switch (currentState) {
      case 'listening':
        return {
          scale: emotionAnimations.scaleVariation * (1.1 + (baseIntensity * 0.2)),
          pulseSpeed: `${1.5 / animationSpeed}s`,
          glowIntensity: voiceVisuals.pulseIntensity,
          rotationSpeed: `${8 / animationSpeed}s`,
          innerGlow: true,
          pulseRings: true,
          glowRadius: voiceVisuals.glowRadius
        };
      case 'processing':
        return {
          scale: emotionAnimations.scaleVariation * (1.0 + (baseIntensity * 0.15)),
          pulseSpeed: `${2 / animationSpeed}s`,
          glowIntensity: voiceVisuals.pulseIntensity * 0.9,
          rotationSpeed: `${4 / animationSpeed}s`,
          innerGlow: true,
          swirling: true,
          glowRadius: voiceVisuals.glowRadius
        };
      case 'speaking':
        return {
          scale: emotionAnimations.scaleVariation * (1.15 + (baseIntensity * 0.25)),
          pulseSpeed: `${1 / animationSpeed}s`,
          glowIntensity: voiceVisuals.pulseIntensity,
          rotationSpeed: `${6 / animationSpeed}s`,
          innerGlow: true,
          expanding: true,
          glowRadius: voiceVisuals.glowRadius
        };
      default: // idle
        return {
          scale: emotionAnimations.scaleVariation,
          pulseSpeed: `${4 / animationSpeed}s`,
          glowIntensity: voiceVisuals.pulseIntensity * 0.6,
          rotationSpeed: `${12 / animationSpeed}s`,
          innerGlow: false,
          gentle: true,
          glowRadius: voiceVisuals.glowRadius * 0.5
        };
    }
  };

  const effects = getStateEffects();

  return (
    <div 
      className={`relative flex items-center justify-center ${className}`}
      style={{ 
        width: config.glowSize, 
        height: config.glowSize 
      }}
    >
      {/* Outer glow rings for active states */}
      {effects.pulseRings && (
        <>
          <div 
            className="absolute rounded-full border-2 opacity-30 animate-ping"
            style={{
              width: config.glowSize * 0.8,
              height: config.glowSize * 0.8,
              borderColor: `rgb(133, 38, 22, ${effects.glowIntensity * 0.3})`,
              animationDuration: effects.pulseSpeed,
              animationDelay: '0s'
            }}
          />
          <div 
            className="absolute rounded-full border-2 opacity-20 animate-ping"
            style={{
              width: config.glowSize * 0.9,
              height: config.glowSize * 0.9,
              borderColor: `rgb(218, 113, 52, ${effects.glowIntensity * 0.2})`,
              animationDuration: effects.pulseSpeed,
              animationDelay: '0.5s'
            }}
          />
          <div 
            className="absolute rounded-full border-2 opacity-10 animate-ping"
            style={{
              width: config.glowSize,
              height: config.glowSize,
              borderColor: `rgb(232, 145, 84, ${effects.glowIntensity * 0.1})`,
              animationDuration: effects.pulseSpeed,
              animationDelay: '1s'
            }}
          />
        </>
      )}

      {/* Main orb container */}
      <div
        ref={orbRef}
        className="relative rounded-full transition-all duration-500 ease-out"
        style={{
          width: config.width,
          height: config.height,
          transform: `scale(${effects.scale})`,
          filter: `drop-shadow(0 0 ${20 * effects.glowIntensity}px rgba(133, 38, 22, ${effects.glowIntensity * 0.4}))`
        }}
      >
        {/* Base orb with glass effect */}
        <div
          className={`
            absolute inset-0 rounded-full
            bg-gradient-to-br ${voiceVisuals.visualState.colors.primary}
            transition-all duration-1000 ease-out
          `}
          style={{
            opacity: emotionAnimations.opacityVariation + (voiceVisuals.visualState.colors.vibrance * 0.2),
            animation: `orb-rotation ${effects.rotationSpeed} linear infinite`,
            filter: `brightness(${emotionAnimations.colorIntensity}) saturate(${1 + voiceVisuals.intensity * 0.5})`
          }}
        />

        {/* Secondary gradient layer */}
        <div
          className={`
            absolute inset-0 rounded-full
            bg-gradient-to-tl ${voiceVisuals.visualState.colors.secondary}
            transition-all duration-1000 ease-out
          `}
          style={{
            opacity: (emotionAnimations.opacityVariation * 0.8) + (voiceVisuals.visualState.colors.vibrance * 0.15),
            animation: `orb-rotation ${effects.rotationSpeed} linear infinite reverse`,
            filter: `brightness(${emotionAnimations.colorIntensity * 0.9})`
          }}
        />

        {/* Accent gradient overlay */}
        <div
          className={`
            absolute inset-0 rounded-full
            bg-gradient-to-r ${voiceVisuals.visualState.colors.accent}
            transition-all duration-1000 ease-out
          `}
          style={{
            opacity: (emotionAnimations.opacityVariation * 0.6) + (voiceVisuals.visualState.colors.vibrance * 0.1),
            animation: `orb-pulse ${effects.pulseSpeed} ease-in-out infinite`,
            filter: `brightness(${emotionAnimations.colorIntensity * 0.8})`
          }}
        />

        {/* Glass reflection effect */}
        <div 
          className="absolute inset-0 rounded-full"
          style={{
            background: `
              radial-gradient(circle at 30% 30%, 
                rgba(255, 255, 255, 0.8) 0%, 
                rgba(255, 255, 255, 0.4) 20%, 
                rgba(255, 255, 255, 0.1) 40%, 
                transparent 70%
              )
            `
          }}
        />

        {/* Inner highlight for depth */}
        <div 
          className="absolute inset-2 rounded-full"
          style={{
            background: `
              radial-gradient(circle at 40% 40%, 
                rgba(255, 255, 255, 0.6) 0%, 
                rgba(255, 255, 255, 0.2) 30%, 
                transparent 60%
              )
            `
          }}
        />

        {/* Bottom shadow for 3D effect */}
        <div 
          className="absolute inset-0 rounded-full"
          style={{
            background: `
              radial-gradient(ellipse at 50% 80%, 
                rgba(0, 0, 0, 0.3) 0%, 
                rgba(0, 0, 0, 0.1) 40%, 
                transparent 70%
              )
            `
          }}
        />

        {/* Inner glow for active states */}
        {effects.innerGlow && (
          <div 
            className={`
              absolute inset-1 rounded-full
              bg-gradient-to-br ${visualState.colors.primary}
              animate-pulse
            `}
            style={{
              opacity: 0.3 * effects.glowIntensity,
              animationDuration: effects.pulseSpeed
            }}
          />
        )}

        {/* Swirling effect for processing */}
        {effects.swirling && (
          <div 
            className={`
              absolute inset-0 rounded-full
              bg-gradient-conic from-transparent via-white/20 to-transparent
            `}
            style={{
              animation: `orb-swirl 2s linear infinite`
            }}
          />
        )}

        {/* Expanding rings for speaking */}
        {effects.expanding && (
          <>
            <div 
              className="absolute inset-0 rounded-full border-2 border-white/30 animate-ping"
              style={{ animationDuration: '0.8s' }}
            />
            <div 
              className="absolute inset-2 rounded-full border-2 border-white/20 animate-ping"
              style={{ animationDuration: '1.2s', animationDelay: '0.2s' }}
            />
          </>
        )}
      </div>

      {/* Floating particles for enhanced effect */}
      {(isSpeaking || isProcessing) && (
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-white/40 rounded-full animate-float"
              style={{
                left: `${20 + (i * 10)}%`,
                top: `${30 + (i * 8)}%`,
                animationDelay: `${i * 0.3}s`,
                animationDuration: `${3 + (i * 0.5)}s`
              }}
            />
          ))}
        </div>
      )}

      <style jsx>{`
        @keyframes orb-rotation {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        @keyframes orb-pulse {
          0%, 100% { opacity: 0.4; transform: scale(1); }
          50% { opacity: 0.7; transform: scale(1.05); }
        }

        @keyframes orb-swirl {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        @keyframes float {
          0%, 100% { 
            transform: translateY(0px) translateX(0px); 
            opacity: 0.4; 
          }
          25% { 
            transform: translateY(-10px) translateX(5px); 
            opacity: 0.8; 
          }
          50% { 
            transform: translateY(-20px) translateX(-5px); 
            opacity: 1; 
          }
          75% { 
            transform: translateY(-10px) translateX(3px); 
            opacity: 0.6; 
          }
        }
      `}</style>
    </div>
  );
}
