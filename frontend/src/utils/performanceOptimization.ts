/**
 * Performance optimization utilities for the voice interface
 * Ensures smooth 60fps animations and efficient rendering
 */

import React, { useCallback, useEffect, useRef, useState } from 'react';

// Performance monitoring
export interface PerformanceMetrics {
  fps: number;
  frameTime: number;
  isOptimal: boolean;
  shouldReduceEffects: boolean;
}

/**
 * Hook for monitoring and optimizing performance
 */
export function usePerformanceMonitor(): PerformanceMetrics {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 60,
    frameTime: 16.67,
    isOptimal: true,
    shouldReduceEffects: false
  });

  const frameCountRef = useRef(0);
  const lastTimeRef = useRef(performance.now());
  const fpsHistoryRef = useRef<number[]>([]);

  useEffect(() => {
    let animationId: number;

    const measurePerformance = () => {
      const now = performance.now();
      const deltaTime = now - lastTimeRef.current;
      
      frameCountRef.current++;
      
      // Calculate FPS every second
      if (deltaTime >= 1000) {
        const fps = Math.round((frameCountRef.current * 1000) / deltaTime);
        const frameTime = deltaTime / frameCountRef.current;
        
        // Keep FPS history for trend analysis
        fpsHistoryRef.current.push(fps);
        if (fpsHistoryRef.current.length > 10) {
          fpsHistoryRef.current.shift();
        }
        
        // Calculate average FPS
        const avgFps = fpsHistoryRef.current.reduce((sum, f) => sum + f, 0) / fpsHistoryRef.current.length;
        
        setMetrics({
          fps: Math.round(avgFps),
          frameTime,
          isOptimal: avgFps >= 55,
          shouldReduceEffects: avgFps < 45
        });
        
        frameCountRef.current = 0;
        lastTimeRef.current = now;
      }
      
      animationId = requestAnimationFrame(measurePerformance);
    };

    animationId = requestAnimationFrame(measurePerformance);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, []);

  return metrics;
}

/**
 * Throttle function for expensive operations
 */
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastCallRef = useRef<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout>();

  return useCallback((...args: Parameters<T>) => {
    const now = Date.now();
    
    if (now - lastCallRef.current >= delay) {
      lastCallRef.current = now;
      return callback(...args);
    } else {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        lastCallRef.current = Date.now();
        callback(...args);
      }, delay - (now - lastCallRef.current));
    }
  }, [callback, delay]) as T;
}

/**
 * Debounce function for rapid state changes
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Optimized animation frame hook
 */
export function useAnimationFrame(callback: (deltaTime: number) => void, deps: any[] = []) {
  const requestRef = useRef<number>();
  const previousTimeRef = useRef<number>();
  const callbackRef = useRef(callback);

  // Update callback ref when callback changes
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  const animate = useCallback((time: number) => {
    if (previousTimeRef.current !== undefined) {
      const deltaTime = time - previousTimeRef.current;
      callbackRef.current(deltaTime);
    }
    previousTimeRef.current = time;
    requestRef.current = requestAnimationFrame(animate);
  }, []);

  useEffect(() => {
    requestRef.current = requestAnimationFrame(animate);
    return () => {
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    };
  }, deps);
}

/**
 * Memory-efficient object pooling for animations
 */
export class ObjectPool<T> {
  private pool: T[] = [];
  private createFn: () => T;
  private resetFn: (obj: T) => void;

  constructor(createFn: () => T, resetFn: (obj: T) => void, initialSize: number = 10) {
    this.createFn = createFn;
    this.resetFn = resetFn;
    
    // Pre-populate pool
    for (let i = 0; i < initialSize; i++) {
      this.pool.push(this.createFn());
    }
  }

  get(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!;
    }
    return this.createFn();
  }

  release(obj: T): void {
    this.resetFn(obj);
    this.pool.push(obj);
  }

  clear(): void {
    this.pool.length = 0;
  }
}

/**
 * Optimized CSS transform utilities
 */
export const optimizedTransforms = {
  // Use transform3d to trigger hardware acceleration
  translate3d: (x: number, y: number, z: number = 0) => 
    `translate3d(${x}px, ${y}px, ${z}px)`,
  
  scale3d: (x: number, y: number = x, z: number = 1) => 
    `scale3d(${x}, ${y}, ${z})`,
  
  rotate3d: (x: number, y: number, z: number, angle: number) => 
    `rotate3d(${x}, ${y}, ${z}, ${angle}deg)`,
  
  // Combined transforms for better performance
  translateScale: (tx: number, ty: number, scale: number) => 
    `translate3d(${tx}px, ${ty}px, 0) scale3d(${scale}, ${scale}, 1)`,
  
  rotateScale: (rotation: number, scale: number) => 
    `rotate3d(0, 0, 1, ${rotation}deg) scale3d(${scale}, ${scale}, 1)`
};

/**
 * Performance-aware component wrapper
 */
export function withPerformanceOptimization<P extends object>(
  Component: React.ComponentType<P>
): React.ComponentType<P & { enableOptimizations?: boolean }> {
  return function OptimizedComponent({ enableOptimizations = true, ...props }: P & { enableOptimizations?: boolean }) {
    const metrics = usePerformanceMonitor();
    
    // Reduce effects if performance is poor
    const optimizedProps = enableOptimizations && metrics.shouldReduceEffects 
      ? {
          ...props,
          // Reduce animation complexity
          bubbleCount: Math.floor((props as any).bubbleCount * 0.6),
          intensity: Math.min((props as any).intensity * 0.8, 0.6),
          // Disable expensive effects
          enableGlow: false,
          enableParticles: false
        }
      : props;

    return <Component {...optimizedProps as P} />;
  };
}

/**
 * Error boundary for voice interface components
 */
import React from 'react';

export class VoiceInterfaceErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode; fallback?: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Voice Interface Error:', error, errorInfo);
    
    // Log to error reporting service in production
    if (process.env.NODE_ENV === 'production') {
      // Example: Sentry.captureException(error, { extra: errorInfo });
    }
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="flex items-center justify-center h-full">
          <div className="text-center p-8">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-red-600 text-2xl">⚠️</span>
            </div>
            <h3 className="text-lg font-medium text-eclipse-950 mb-2">
              Voice Interface Error
            </h3>
            <p className="text-eclipse-950/60 mb-4">
              Something went wrong with the voice interface. Please refresh the page.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="btn-primary"
            >
              Refresh Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Accessibility utilities
 */
export const accessibilityUtils = {
  // Screen reader announcements
  announceToScreenReader: (message: string) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  },

  // Voice state announcements
  announceVoiceState: (state: 'listening' | 'processing' | 'speaking' | 'idle') => {
    const messages = {
      listening: 'ORA is listening. Please speak now.',
      processing: 'ORA is processing your message.',
      speaking: 'ORA is responding.',
      idle: 'Voice chat is ready. Tap to begin.'
    };
    
    accessibilityUtils.announceToScreenReader(messages[state]);
  },

  // Keyboard navigation support
  handleKeyboardNavigation: (event: React.KeyboardEvent, onActivate: () => void) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      onActivate();
    }
  }
};

/**
 * Reduced motion preferences
 */
export function useReducedMotion(): boolean {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
}
